@import

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <style>
      /* url("https://fonts.googleapis.com/css2?family=Rubik+Moonrocks&display=swap"); */

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: cursive;
        line-height: 1.5;
      }

      img {
        display: block;
        max-width: 100%;
        height: auto;
      }

      h1 {
        font-size: 40px;
        grid-area: title;
        /* mix-blend-mode: multiply; */
        color: rgb(126, 56, 56);
        /* background-color: yellow; */
      }
      p {
        grid-area: text;
      }
      img {
        grid-area: image;
        z-index: -1;
        filter: drop-shadow(0 0 16px rgba(0, 0, 0, 0.5));
      }
      .bear {
        animation: card 2s ease-out alternate infinite;
        --span: 50px;
        padding: 20px;
        display: grid;
        gap: 1rem;
        overflow: hidden;
        max-width: 700px;
        border: 10px solid;
        border-radius: 10px;
        isolation: isolate;
        position: relative;
        background-image: linear-gradient(
            to right,
            rgba(49, 14, 14, 0.8),
            rgba(255, 255, 255, 0.8)
          ),
          url(imageq.png);
        grid-template-columns:
          [title-start text-start] 1fr
          [text-end image-start] 1fr
          [title-end image-end];
        grid-template-rows:
          [title-start] auto
          [image-start] var(--span)
          [title-end text-start] auto
          [image-end text-end];
      }
      svg {
        width: 100%;
        height: ;
        fill: yellow;
        position: absolute;
        grid-column: text-start;
        grid-row: text-start;
        left: 0;
        z-index: -1;
        opacity: 0.3;
      }

      @keyframes card {
        to {
          
          /* grid-template-rows:
            [title-start] auto
            [image-start] 100px
            [title-end text-start] auto
            [image-end text-end]; */
        }
      }
    </style>
    <div class="bear">
      <h1>
        Step up your sim yoyoy & Lorem ipsum dolor Lorem ipsum Lorem ipsum dolor
      </h1>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Ea ad libero,
        incidunt officia esse sed ipsum neque perferendis corrupti ducimus.
      </p>
      <img src="imageq.png" alt="" />
      <svg viewBox="0 0 24 24" fill="pink">
        <path
          d="M8 17H16M8 17C8 18.1046 7.10457 19 6 19C4.89543 19 4 18.1046 4 17M8 17C8 15.8954 7.10457 15 6 15C4.89543 15 4 15.8954 4 17M16 17C16 18.1046 16.8954 19 18 19C19.1046 19 20 18.1046 20 17M16 17C16 15.8954 16.8954 15 18 15C19.1046 15 20 15.8954 20 17M10 5V11M4 11L4.33152 9.01088C4.56901 7.58593 4.68776 6.87345 5.0433 6.3388C5.35671 5.8675 5.79705 5.49447 6.31346 5.26281C6.8993 5 7.6216 5 9.06621 5H12.4311C13.3703 5 13.8399 5 14.2662 5.12945C14.6436 5.24406 14.9946 5.43194 15.2993 5.68236C15.6435 5.96523 15.904 6.35597 16.425 7.13744L19 11M4 17H3.6C3.03995 17 2.75992 17 2.54601 16.891C2.35785 16.7951 2.20487 16.6422 2.10899 16.454C2 16.2401 2 15.9601 2 15.4V14.2C2 13.0799 2 12.5198 2.21799 12.092C2.40973 11.7157 2.71569 11.4097 3.09202 11.218C3.51984 11 4.0799 11 5.2 11H17.2C17.9432 11 18.3148 11 18.6257 11.0492C20.3373 11.3203 21.6797 12.6627 21.9508 14.3743C22 14.6852 22 15.0568 22 15.8C22 15.9858 22 16.0787 21.9877 16.1564C21.9199 16.5843 21.5843 16.9199 21.1564 16.9877C21.0787 17 20.9858 17 20.8 17H20"
          stroke="#000000"
        />
      </svg>
    </div>
    <div style="height: 400px; width: 200px; background-color: red"></div>
  </body>
</html>
