:after,
:before,
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --direction: column;
  --theme: dark;

  &:has([type="checkbox"]:checked) {
    --theme: light;
  }
}


html {
  color-scheme: var(--theme);
}


img {
  max-width: 100%;
  display: block;
  object-fit: cover;
  /* height: 100%; */
}


.list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}


.card {
  --size: attr(size);
  container: card / inline-size;
  display: grid;
  flex-direction: var(--direction);
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: max-content 100px;
  border: 1px solid;
  padding: 1rem;
  gap: 1rem;
  position: relative;
  /* z-index: 1; */
  .card-info:before {
    content: var(--size);
    position: absolute;
    color: white;
    /* padding: 0.5rem; */
    background-color: black;
    /* right: 0px; */
    /* bottom: 0; */
    width: 100%;
    font-size: var(--size);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    /* overflow: hidden; */

  }

  :first-child {
    grid-column: 1 / -1;
    grid-row: 1 / 2;
  }

  :last-child {
    grid-column: 1 / -1;
    grid-row: 2 / 3;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

@keyframes appear {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}




@container style(--theme: dark) {
  .card {
    background: rgb(60, 170, 16) !important;
    color: #333;
  }
}