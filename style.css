* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --color-scheme: dark;
}


html {
  color-scheme: var(--color-scheme);
}


img {
  max-width: 100%;
  display: block;
  object-fit: cover;
  /* height: 100%; */
}


.list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}


.card {
  --direction: column;
  container: card / inline-size;
  /* width: 300px; */
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: max-content 100px;
  /* flex-direction: var(--direction); */
  border: 1px solid;
  padding: 1rem;
  gap: 1rem;

  :first-child {
    grid-column: 1 / -1;
    grid-row: 1 / 2;
  }

  :last-child {
    grid-column: 1 / -1;
    grid-row: 2 / 3;
  }


  @container card (width > 450px) {
    :first-child {
      grid-column: 2 / 4;
      grid-row: 1 / 3;
    }

    :last-child {
      grid-column: 1 / 2;
      grid-row: 1 / 3;
    }
  }


  img {
    height: 100%;
    object-fit: cover;
    width: 100%;
    background-color: #a53333;
  }
}

:has([type="checkbox"]:checked) {
  --color-scheme: light;
}

@container style(--color-scheme: light) {
  body {
    color: red !important;
  }
}


/* @keyframes scheme {
  0%, 100% {
    --color-scheme: dark;
  }

  50% {
    --color-scheme: light;
  }
} */

html {
  animation: scheme 2s infinite;
}